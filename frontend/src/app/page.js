'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { postAPI } from '@/utils/api';
import { subscribeToNewPosts } from '@/utils/socket';
import { useToast } from '@/contexts/ToastContext';
import Button from '@/components/Button';
import Post from '@/components/Post';
import styles from '@/styles/Home.module.css';

export default function Home() {
  const { isAuthenticated, user } = useAuth();
  const { showSuccess, showNotification } = useToast();
  const [posts, setPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  console.log('🏠 Home component rendered, showSuccess:', typeof showSuccess);

  useEffect(() => {
    // Fetch feed posts if user is authenticated
    if (isAuthenticated) {
      fetchFeed();
    } else {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Real-time WebSocket event listeners
  useEffect(() => {
    if (isAuthenticated) {
      // Subscribe to new posts
      const unsubscribeNewPosts = subscribeToNewPosts((data) => {
        // Add new post to the beginning of the feed (only if it's not from the current user)
        if (data.post && data.post.author.id !== user?.id) {
          setPosts(prev => [data.post, ...prev]);
        }
      });

      return () => {
        unsubscribeNewPosts();
      };
    }
  }, [isAuthenticated, user?.id]);

  const fetchFeed = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching feed...');
      const response = await postAPI.getFeed();
      console.log('Feed response:', response.data);
      console.log('Posts from response:', response.data.data?.posts);
      setPosts(response.data.data?.posts || []);
    } catch (error) {
      console.error('Error fetching feed:', error);
      console.error('Error details:', error.response?.data);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeletePost = (postId) => {
    setPosts(prev => prev.filter(post => post.id !== postId));
  };

  const testToast = () => {
    console.log('🧪 Testing toast system - button clicked');
    try {
      console.log('🧪 Calling showSuccess...');
      showSuccess('Test toast is working!');
      console.log('🧪 showSuccess called successfully');
    } catch (error) {
      console.error('🧪 Error calling showSuccess:', error);
    }
  };

  // Guest home page
  if (!isAuthenticated) {
    return (
      <div className={styles.guestContainer}>
        <div className={styles.heroSection}>
          <h1 className={styles.heroTitle}>Connect with friends and the world around you</h1>
          <p className={styles.heroSubtitle}>
            Join our social network to share, connect, and discover
          </p>
          <div className={styles.heroCta}>
            <Link href="/auth/register">
              <Button variant="primary" size="large">
                Sign Up
              </Button>
            </Link>
            <Link href="/auth/login">
              <Button variant="outline" size="large">
                Log In
              </Button>
            </Link>
          </div>
        </div>
        <div className={styles.featuresSection}>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>👥</div>
            <h2>Connect with Friends</h2>
            <p>Follow friends and family to stay updated with their lives</p>
          </div>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>📝</div>
            <h2>Share Your Thoughts</h2>
            <p>Post updates, photos, and more to share with your network</p>
          </div>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>💬</div>
            <h2>Real-time Chat</h2>
            <p>Message friends instantly with our real-time chat system</p>
          </div>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>👥</div>
            <h2>Join Groups</h2>
            <p>Find communities of people who share your interests</p>
          </div>
        </div>
      </div>
    );
  }

  // Authenticated home page (feed)
  return (
    <div className={styles.feedContainer}>
      {isLoading ? (
        <div className={styles.loading}>Loading feed...</div>
      ) : posts.length === 0 ? (
        <div className={styles.emptyFeed}>
          <h2>Your feed is empty</h2>
          <p>Follow more people or create a post to see content here</p>
          <Link href="/posts/create">
            <Button variant="primary">Create a Post</Button>
          </Link>
        </div>
      ) : (
        <div className={styles.feedContent}>
          <div className={styles.feedHeader}>
            <h1 className={styles.feedTitle}>Your Feed</h1>
            <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
              <Button onClick={testToast} variant="secondary" size="small">
                🧪 Test Toast
              </Button>
              <Link href="/posts/create">
                <Button variant="primary">Create Post</Button>
              </Link>
            </div>
          </div>

          <div className={styles.postsContainer}>
            {posts.map((post, index) => (
              <Post
                key={post.id}
                post={post}
                onDelete={handleDeletePost}
                priority={index < 2} // First 2 posts get priority loading
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
