// Simple test script to verify notification system
// Run this in the browser console to test the notification flow

async function testNotificationFlow() {
  console.log('🧪 Testing notification flow...');
  
  try {
    // Get current user info
    const authResponse = await fetch('/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!authResponse.ok) {
      console.error('❌ Not authenticated');
      return;
    }
    
    const currentUser = await authResponse.json();
    console.log('✅ Current user:', currentUser.data.user);
    
    // Get feed to find posts
    const feedResponse = await fetch('/api/posts/feed?page=1&limit=10', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!feedResponse.ok) {
      console.error('❌ Failed to get feed');
      return;
    }
    
    const feedData = await feedResponse.json();
    const posts = feedData.data.posts || [];
    console.log('📝 Available posts:', posts.length);
    
    if (posts.length === 0) {
      console.log('ℹ️ No posts available to like');
      return;
    }
    
    // Find a post that belongs to another user
    const otherUserPost = posts.find(post => post.author.id !== currentUser.data.user.id);
    
    if (!otherUserPost) {
      console.log('ℹ️ No posts from other users found');
      return;
    }
    
    console.log('🎯 Found post to like:', otherUserPost.id, 'by', otherUserPost.author.fullName);
    
    // Like the post
    const likeResponse = await fetch(`/api/posts/${otherUserPost.id}/like`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (likeResponse.ok) {
      console.log('✅ Post liked successfully! Notification should be sent to:', otherUserPost.author.fullName);
    } else {
      const errorData = await likeResponse.json();
      console.log('❌ Failed to like post:', errorData.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testNotificationFlow();
